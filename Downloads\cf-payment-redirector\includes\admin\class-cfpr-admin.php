<?php
/**
 * Admin
 *
 * @package CF_Payment_Redirector
 */

// Sair se acessado diretamente.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe Admin.
 */
class CFPR_Admin {

    /**
     * Variável de instância.
     *
     * @var CFPR_Admin
     */
    private static $instance;

    /**
     * Slug do menu.
     *
     * @var string
     */
    private $menu_slug = 'cf-payment-redirector';

    /**
     * Inicializador.
     */
    public static function get_instance() {
        if ( ! isset( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Construtor.
     */
    public function __construct() {
        add_action( 'admin_menu', array( $this, 'setup_menu' ) );
        add_action( 'admin_init', array( $this, 'register_settings' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'admin_scripts' ) );
        
        // Hook adicional para garantir que os estilos sejam carregados
        add_action( 'admin_head', array( $this, 'force_load_styles' ) );
        
        // AJAX para obter passos de um funil
        add_action( 'wp_ajax_cfpr_get_flow_steps', array( $this, 'ajax_get_flow_steps' ) );
        
        // AJAX para limpar logs
        add_action( 'wp_ajax_cfpr_clear_logs', array( $this, 'ajax_clear_logs' ) );
        
        // Debug do menu - remover em produção
        add_action( 'admin_notices', array( $this, 'debug_menu_slugs' ) );
    }

    /**
     * Configurar menu admin.
     */
    public function setup_menu() {
        // Adicionar apenas menu independente para evitar conflitos
        add_menu_page(
            __( 'Redirecionador TunaPix', 'cf-payment-redirector' ),
            __( 'Redirecionador TunaPix', 'cf-payment-redirector' ),
            'manage_options',
            'cfpr-settings',
            array( $this, 'render_admin_page' ),
            'dashicons-randomize',
            59 // Posicionar logo abaixo do WooCommerce
        );
    }

    /**
     * Registrar configurações.
     */
    public function register_settings() {
        register_setting(
            'cfpr_settings',
            'cfpr_redirect_rules',
            array(
                'sanitize_callback' => array( $this, 'sanitize_settings' ),
                'capability' => 'manage_options',
            )
        );
    }

    /**
     * Sanitizar configurações.
     *
     * @param array $input Input do formulário.
     * @return array Dados sanitizados.
     */
    public function sanitize_settings( $input ) {
        $sanitized_input = array();
        
        if ( ! is_array( $input ) ) {
            return $sanitized_input;
        }
        
        foreach ( $input as $flow_id => $payment_rules ) {
            $flow_id = absint( $flow_id );
            
            if ( empty( $flow_id ) ) {
                continue;
            }
            
            $sanitized_input[ $flow_id ] = array();
            
            foreach ( $payment_rules as $payment_method => $step_id ) {
                $payment_method = sanitize_text_field( $payment_method );
                $step_id = absint( $step_id );
                
                if ( ! empty( $payment_method ) ) {
                    $sanitized_input[ $flow_id ][ $payment_method ] = $step_id;
                }
            }
        }
        
        return $sanitized_input;
    }

    /**
     * Enfileirar scripts admin.
     *
     * @param string $hook Hook atual.
     */
    public function admin_scripts( $hook ) {
        // Verificação mais simples e robusta - usar apenas o parâmetro GET
        $is_our_page = false;
        
        // Verificar se estamos em uma das nossas páginas pelo parâmetro GET
        if ( isset( $_GET['page'] ) ) {
            $page_param = sanitize_text_field( $_GET['page'] );
            
            // Lista de páginas válidas
            $valid_pages = array(
                $this->menu_slug,           // cf-payment-redirector
                'cfpr-settings',            // menu independente
            );
            
            if ( in_array( $page_param, $valid_pages ) ) {
                $is_our_page = true;
            }
        }
        
        // Se não for nossa página, não carregar os assets
        if ( ! $is_our_page ) {
            return;
        }
        
        // Debug - mostrar que os estilos estão sendo carregados
        if ( current_user_can( 'manage_options' ) ) {
            add_action( 'admin_notices', function() use ( $hook ) {
                echo '<div class="notice notice-success is-dismissible">';
                echo '<p><strong>CFPR:</strong> Estilos carregados! Hook: ' . esc_html( $hook ) . '</p>';
                echo '</div>';
            });
        }
        
        // Enfileirar jQuery e scripts necessários
        wp_enqueue_script( 'jquery' );
        wp_enqueue_script( 'jquery-ui-tabs' );
        
        // Enfileirar nosso script admin
        wp_enqueue_script(
            'cfpr-admin-js',
            CFPR_URL . 'assets/js/admin.js',
            array( 'jquery' ),
            CFPR_VER,
            true
        );
        
        // Enviar dados para o script
        wp_localize_script(
            'cfpr-admin-js',
            'cfpr_admin',
            array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'cfpr_admin_nonce' ),
            )
        );
        
        // Enfileirar estilos admin
        wp_enqueue_style(
            'cfpr-admin-css',
            CFPR_URL . 'assets/css/admin.css',
            array(),
            CFPR_VER
        );
    }

    /**
     * Renderizar página admin.
     */
    public function render_admin_page() {
        // Verificar permissões básicas
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Sem permissão para acessar esta página.', 'cf-payment-redirector' ) );
        }
        
        ?>
        <div class="wrap cfpr-admin-wrap">
            <div class="cfpr-header">
                <div class="cfpr-header-content">
                    <h1><?php echo esc_html__( 'Redirecionamento de Pagamento', 'cf-payment-redirector' ); ?></h1>
                    <p class="cfpr-subtitle"><?php echo esc_html__( 'Configure redirecionamentos automáticos baseados no método de pagamento', 'cf-payment-redirector' ); ?></p>
                </div>
            </div>
            
            <div class="cfpr-admin-content">
                <form method="post" action="options.php" class="cfpr-form">
                    <?php settings_fields( 'cfpr_settings' ); ?>
                    <?php $this->render_settings_form(); ?>
                    
                    <div class="cfpr-form-footer">
                        <?php submit_button( __( 'Salvar', 'cf-payment-redirector' ), 'primary', 'submit', false, array( 'class' => 'cfpr-save-btn' ) ); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Renderizar formulário de configurações.
     */
    private function render_settings_form() {
        // Verificar se as classes necessárias existem
        if ( ! class_exists( 'CFPR_Integration' ) ) {
            echo '<div class="cfpr-empty-state">';
            echo '<div class="cfpr-empty-icon">⚠️</div>';
            echo '<h3>' . esc_html__( 'Erro de integração', 'cf-payment-redirector' ) . '</h3>';
            echo '<p>' . esc_html__( 'A classe de integração não foi encontrada. Verifique se o plugin está instalado corretamente.', 'cf-payment-redirector' ) . '</p>';
            echo '</div>';
            return;
        }
        
        $integration = CFPR_Integration::get_instance();
        $flows = $integration->get_flows();
        $payment_gateways = $integration->get_available_payment_gateways();
        $redirect_rules = get_option( 'cfpr_redirect_rules', array() );
        
        if ( empty( $flows ) ) {
            echo '<div class="cfpr-empty-state">';
            echo '<div class="cfpr-empty-icon">📋</div>';
            echo '<h3>' . esc_html__( 'Nenhum funil encontrado', 'cf-payment-redirector' ) . '</h3>';
            echo '<p>' . esc_html__( 'Crie alguns funis no CartFlows para começar a configurar os redirecionamentos.', 'cf-payment-redirector' ) . '</p>';
            echo '</div>';
            return;
        }
        
        if ( empty( $payment_gateways ) ) {
            echo '<div class="cfpr-empty-state">';
            echo '<div class="cfpr-empty-icon">💳</div>';
            echo '<h3>' . esc_html__( 'Nenhum método de pagamento encontrado', 'cf-payment-redirector' ) . '</h3>';
            echo '<p>' . esc_html__( 'Configure métodos de pagamento no WooCommerce primeiro.', 'cf-payment-redirector' ) . '</p>';
            echo '</div>';
            return;
        }
        
        // Configuração da paginação
        $flows_per_page = 4;
        $current_page = isset( $_GET['cfpr_page'] ) ? max( 1, intval( $_GET['cfpr_page'] ) ) : 1;
        $total_flows = count( $flows );
        $total_pages = ceil( $total_flows / $flows_per_page );
        $offset = ( $current_page - 1 ) * $flows_per_page;
        
        // Obter funis da página atual
        $flows_array = array_slice( $flows, $offset, $flows_per_page, true );
        
        // Container principal
        echo '<div class="cfpr-main-container">';
        
        // Cabeçalho da seção
        echo '<div class="cfpr-section-header">';
        echo '<h2 class="cfpr-section-title">' . esc_html__( 'Redirecionamento de Pagamento', 'cf-payment-redirector' ) . '</h2>';
        echo '<p class="cfpr-section-description">' . esc_html__( 'Configure o redirecionamento para cada tipo de checkout.', 'cf-payment-redirector' ) . '</p>';
        echo '</div>';
        
        // Seções de funis
        foreach ( $flows_array as $flow_id => $flow_title ) {
            echo '<div class="cfpr-flow-section" data-flow-id="' . esc_attr( $flow_id ) . '">';
            
            // Título da seção
            echo '<div class="cfpr-flow-section-header">';
            echo '<h3 class="cfpr-flow-section-title">' . esc_html( $flow_title ) . '</h3>';
            echo '</div>';
            
            // Conteúdo da seção
            echo '<div class="cfpr-flow-section-content">';
            
            // Grid de métodos de pagamento
            echo '<div class="cfpr-payment-grid">';
            
            foreach ( $payment_gateways as $gateway_id => $gateway_title ) {
                echo '<div class="cfpr-payment-item">';
                
                // Label do método de pagamento
                echo '<div class="cfpr-payment-label">';
                echo '<span>' . esc_html( $gateway_title ) . '</span>';
                echo '</div>';
                
                // Select do redirecionamento
                echo '<div class="cfpr-payment-select">';
                
                // Valor atual da configuração
                $current_value = isset( $redirect_rules[ $flow_id ][ $gateway_id ] ) ? $redirect_rules[ $flow_id ][ $gateway_id ] : '';
                
                // Campo de select para escolher o passo
                echo '<select name="cfpr_redirect_rules[' . esc_attr( $flow_id ) . '][' . esc_attr( $gateway_id ) . ']" class="cfpr-step-select" data-flow-id="' . esc_attr( $flow_id ) . '">';
                echo '<option value="">' . esc_html__( 'Nenhum', 'cf-payment-redirector' ) . '</option>';
                
                // Obter passos deste funil
                $steps = $integration->get_flow_steps( $flow_id );
                
                if ( ! empty( $steps ) ) {
                    foreach ( $steps as $step_id => $step_title ) {
                        echo '<option value="' . esc_attr( $step_id ) . '" ' . selected( $current_value, $step_id, false ) . '>' . esc_html( $step_title ) . '</option>';
                    }
                }
                
                echo '</select>';
                echo '</div>';
                
                echo '</div>'; // payment-item
            }
            
            echo '</div>'; // payment-grid
            echo '</div>'; // flow-section-content
            echo '</div>'; // flow-section
        }
        
        echo '</div>'; // main-container
        
        // Paginação
        if ( $total_pages > 1 ) {
            echo '<div class="cfpr-pagination-wrapper">';
            echo '<div class="cfpr-pagination">';
            
            // Botão anterior
            if ( $current_page > 1 ) {
                $prev_url = add_query_arg( 'cfpr_page', $current_page - 1 );
                echo '<a href="' . esc_url( $prev_url ) . '" class="cfpr-page-btn cfpr-prev-btn">‹ ' . esc_html__( 'Anterior', 'cf-payment-redirector' ) . '</a>';
            }
            
            // Números das páginas
            echo '<div class="cfpr-page-numbers">';
            for ( $i = 1; $i <= $total_pages; $i++ ) {
                $page_url = add_query_arg( 'cfpr_page', $i );
                $active_class = ( $i === $current_page ) ? ' cfpr-page-active' : '';
                echo '<a href="' . esc_url( $page_url ) . '" class="cfpr-page-number' . $active_class . '">' . $i . '</a>';
            }
            echo '</div>';
            
            // Botão próximo
            if ( $current_page < $total_pages ) {
                $next_url = add_query_arg( 'cfpr_page', $current_page + 1 );
                echo '<a href="' . esc_url( $next_url ) . '" class="cfpr-page-btn cfpr-next-btn">' . esc_html__( 'Próximo', 'cf-payment-redirector' ) . ' ›</a>';
            }
            
            echo '</div>'; // pagination
            echo '</div>'; // pagination-wrapper
        }
    }

    /**
     * Forçar carregamento dos estilos se necessário.
     */
    public function force_load_styles() {
        // Verificar se estamos em uma das nossas páginas
        if ( ! isset( $_GET['page'] ) ) {
            return;
        }
        
        $page_param = sanitize_text_field( $_GET['page'] );
        $valid_pages = array( $this->menu_slug, 'cfpr-settings' );
        
        if ( ! in_array( $page_param, $valid_pages ) ) {
            return;
        }
        
        // Sempre carregar os estilos diretamente no head para garantir que funcionem
        echo '<style id="cfpr-admin-inline-css">';
        echo file_get_contents( CFPR_DIR . 'assets/css/admin.css' );
        echo '</style>';
        
        // Carregar jQuery se não estiver carregado
        if ( ! wp_script_is( 'jquery', 'done' ) && ! wp_script_is( 'jquery', 'enqueued' ) ) {
            wp_enqueue_script( 'jquery' );
        }
        
        // Carregar JavaScript inline
        echo '<script type="text/javascript">';
        echo 'var cfpr_admin = {';
        echo '"ajax_url": "' . esc_url( admin_url( 'admin-ajax.php' ) ) . '",';
        echo '"nonce": "' . wp_create_nonce( 'cfpr_admin_nonce' ) . '"';
        echo '};';
        echo '</script>';
        
        echo '<script type="text/javascript">';
        echo file_get_contents( CFPR_DIR . 'assets/js/admin.js' );
        echo '</script>';
    }

    /**
     * AJAX para obter passos de um funil.
     */
    public function ajax_get_flow_steps() {
        // Verificar nonce
        check_ajax_referer( 'cfpr_admin_nonce', 'nonce' );
        
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permissão negada.', 'cf-payment-redirector' ) ) );
        }
        
        $flow_id = isset( $_POST['flow_id'] ) ? absint( $_POST['flow_id'] ) : 0;
        
        if ( empty( $flow_id ) ) {
            wp_send_json_error( array( 'message' => __( 'ID do funil inválido.', 'cf-payment-redirector' ) ) );
        }
        
        $integration = CFPR_Integration::get_instance();
        $steps = $integration->get_flow_steps( $flow_id );
        
        wp_send_json_success( array( 'steps' => $steps ) );
    }

    /**
     * Handler AJAX para limpar logs de depuração
     */
    public function ajax_clear_logs() {
        // Verificar nonce
        check_ajax_referer( 'cfpr_clear_logs', 'nonce' );
        
        // Verificar permissões
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => 'Permissão negada' ) );
        }
        
        // Limpar logs
        update_option( 'cfpr_debug_logs', array() );
        
        wp_send_json_success( array( 'message' => 'Logs limpos com sucesso' ) );
    }

    /**
     * Debug para verificar os slugs dos menus.
     */
    public function debug_menu_slugs() {
        // Remover debug em produção - comentado para evitar conflitos
        return;
    }
} 