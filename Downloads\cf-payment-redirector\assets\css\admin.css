/**
 * Admin CSS para CartFlows Payment Redirector - Design Ultra Minimalista
 */

/* Reset e base */
.cfpr-admin-wrap {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #fafbfc;
    min-height: 100vh;
    width: 100%;
    text-align: left;
}

/* Cabeçalho principal - mais limpo */
.cfpr-header {
    background: #fff;
    padding: 32px 300px;
    border-bottom: 1px solid #eaecf0;
    margin: 0;
    width: 100%;
    text-align: left;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.cfpr-header-content {
    max-width: 1200px;
    width: 100%;
    text-align: left;
}

.cfpr-header h1 {
    margin: 0 0 6px 0;
    font-size: 28px;
    font-weight: 700;
    color: #101828;
    line-height: 1.2;
    letter-spacing: -0.02em;
    text-align: left;
}

.cfpr-subtitle {
    margin: 0;
    font-size: 16px;
    color: #667085;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
}

/* Conteúdo principal */
.cfpr-admin-content {
    padding: 40px;
    margin: 0 auto;
    max-width: 100%;
    width: 100%;
    text-align: left;
}

.cfpr-form {
    max-width: none;
    width: 100%;
    text-align: left;
}

#wpcontent {

    padding-left: 0 !important;
}

/* Container principal - mais moderno */
.cfpr-main-container {
    background: #fff;
    border-radius: 12px;
    border: 1px solid #eaecf0;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1), 0 1px 2px rgba(16, 24, 40, 0.06);
    width: 100%;
    text-align: left;
    padding: 0 !important;
}

/* Cabeçalho da seção - removido para mais minimalismo */
.cfpr-section-header {
    display: none;
}

/* Seções de funis - mais espaçadas */
.cfpr-flow-section {
    border-bottom: 1px solid #f2f4f7;
}

.cfpr-flow-section:last-child {
    border-bottom: none;
}

/* Cabeçalho da seção do funil - mais elegante */
.cfpr-flow-section-header {
    padding: 24px 32px 20px;
    background: #f9fafb;
    border-bottom: 1px solid #eaecf0;
}

.cfpr-flow-section-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #344054;
    letter-spacing: -0.01em;
    text-align: left;
}

/* Conteúdo da seção do funil */
.cfpr-flow-section-content {
    padding: 32px;
}

/* Grid de métodos de pagamento - layout horizontal */
.cfpr-payment-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    align-items: start;
}

/* Item de método de pagamento - layout vertical */
.cfpr-payment-item {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0;
}

/* Label do método de pagamento - mais elegante */
.cfpr-payment-label {
    font-size: 15px;
    font-weight: 500;
    color: #344054;
    margin: 0;
    line-height: 1.4;
    text-align: left;
}

.cfpr-payment-label span {
    display: block;
}

/* Select do redirecionamento - design moderno */
.cfpr-payment-select {
    position: relative;
    width: 100%;
}

.cfpr-step-select {
    width: 100%;
    max-width: none;
    padding: 12px 16px;
    border: 1px solid #d0d5dd;
    border-radius: 8px;
    font-size: 14px;
    color: #344054;
    background: #fff;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667085' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
    font-weight: 400;
    line-height: 1.5;
}

.cfpr-step-select:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 4px rgba(124, 58, 237, 0.1);
}

.cfpr-step-select:hover {
    border-color: #98a2b3;
}

/* Estados vazios - mais elegantes */
.cfpr-empty-state {
    text-align: center;
    padding: 80px 40px;
    background: #fff;
    border-radius: 12px;
    border: 1px solid #eaecf0;
}

.cfpr-empty-icon {
    font-size: 56px;
    margin-bottom: 20px;
    opacity: 0.7;
}

.cfpr-empty-state h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #101828;
    letter-spacing: -0.01em;
}

.cfpr-empty-state p {
    margin: 0;
    font-size: 16px;
    color: #667085;
    line-height: 1.5;
}

/* Paginação - design mais moderno */
.cfpr-pagination-wrapper {
    margin-top: 32px;
    padding: 24px 32px;
    background: #f9fafb;
    border-top: 1px solid #eaecf0;
}

.cfpr-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.cfpr-page-btn,
.cfpr-page-number {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 14px;
    border: 1px solid #d0d5dd;
    border-radius: 8px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    color: #344054;
    background: #fff;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 40px;
    height: 40px;
}

.cfpr-page-btn:hover,
.cfpr-page-number:hover {
    border-color: #98a2b3;
    color: #344054;
    text-decoration: none;
    background: #f9fafb;
    transform: translateY(-1px);
}

.cfpr-page-number.cfpr-page-active {
    background: #7c3aed;
    border-color: #7c3aed;
    color: #fff;
    box-shadow: 0 1px 3px rgba(124, 58, 237, 0.3);
}

.cfpr-page-number.cfpr-page-active:hover {
    background: #6d28d9;
    border-color: #6d28d9;
    color: #fff;
    transform: translateY(-1px);
}

.cfpr-page-numbers {
    display: flex;
    gap: 4px;
}

/* Rodapé do formulário - mais limpo */
.cfpr-form-footer {
    margin-top: 0;
    padding: 32px;
    background: #f9fafb;
    border-top: 1px solid #eaecf0;
    text-align: left;
    width: 100%;
}

/* Botão Salvar - design moderno */
.cfpr-save-btn {
    background: #7c3aed !important;
    border: 1px solid #7c3aed !important;
    color: #fff !important;
    padding: 12px 24px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 1px 3px rgba(124, 58, 237, 0.3) !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    letter-spacing: -0.01em !important;
    min-width: 120px !important;
    justify-content: center !important;
}

.cfpr-save-btn:hover {
    background: #6d28d9 !important;
    border-color: #6d28d9 !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.4) !important;
}

.cfpr-save-btn:focus {
    box-shadow: 0 0 0 4px rgba(124, 58, 237, 0.2) !important;
}

.cfpr-save-btn:active {
    transform: translateY(0) !important;
}

/* Responsividade melhorada */
@media (max-width: 768px) {
    .cfpr-header {
        padding: 24px 20px;
    }

    .cfpr-header-content {
        padding: 0;
    }

    .cfpr-admin-content {
        padding: 24px 20px;
    }

    .cfpr-flow-section-header {
        padding: 20px 24px 16px;
    }

    .cfpr-flow-section-content {
        padding: 24px;
    }

    .cfpr-payment-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .cfpr-payment-item {
        gap: 12px;
    }

    .cfpr-step-select {
        max-width: none;
    }

    .cfpr-form-footer {
        padding: 24px;
    }

    .cfpr-pagination-wrapper {
        padding: 20px 24px;
    }

    .cfpr-pagination {
        flex-wrap: wrap;
        gap: 6px;
    }

    .cfpr-empty-state {
        padding: 60px 24px;
    }
}

/* Estados de loading - mais elegantes */
.cfpr-step-select:disabled {
    background: #f2f4f7;
    color: #98a2b3;
    cursor: not-allowed;
    opacity: 0.7;
}

/* Animações suaves */
.cfpr-step-select,
.cfpr-page-btn,
.cfpr-page-number,
.cfpr-save-btn {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Micro-interações */
.cfpr-flow-section:hover {
    background: rgba(124, 58, 237, 0.01);
}

.cfpr-payment-item:hover .cfpr-step-select {
    border-color: #98a2b3;
}

/* Garantir alinhamento à esquerda para todos os elementos de texto */
* {
    text-align: left;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
span,
label,
div,
select,
option,
button {
    text-align: left !important;
}

/* Garantir largura total para elementos principais */
.cfpr-admin-wrap,
.cfpr-header,
.cfpr-admin-content,
.cfpr-form,
.cfpr-main-container,
.cfpr-flow-section,
.cfpr-flow-section-header,
.cfpr-flow-section-content,
.cfpr-payment-grid,
.cfpr-payment-item,
.cfpr-form-footer {
    width: 100%;
    box-sizing: border-box;
}

/* Centralizar conteúdo principal mantendo largura total */
.cfpr-admin-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* Ajustar responsividade para manter centralização */
@media (min-width: 1240px) {
    .cfpr-admin-content {
        padding: 40px;
    }

    .cfpr-header-content {
        padding: 0 20px;
    }
}