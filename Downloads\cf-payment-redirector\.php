    /**
     * Carregar plugin após todos os plugins carregados.
     */
    public function load_plugin() {
        // Sempre iniciar os componentes, mesmo sem CartFlows
        $this->init_components();
    }    /**
     * Verificar plugins necessários.
     */
    public function check_required_plugins() {
        // Verificar apenas se estamos na página do plugin
        if ( isset( $_GET['page'] ) && ( $_GET['page'] === 'cf-payment-redirector' || $_GET['page'] === 'cfpr-settings' ) ) {
            if ( ! class_exists( 'Cartflows_Loader' ) ) {
                echo '<div class="notice notice-warning"><p>' . __( 'O plugin Redirecionador TunaPix funciona melhor com o plugin CartFlows ativo.', 'cf-payment-redirector' ) . '</p></div>';
            }

            if ( ! class_exists( 'WooCommerce' ) ) {
                echo '<div class="notice notice-error"><p>' . __( 'O plugin Redirecionador TunaPix requer o plugin WooCommerce ativo.', 'cf-payment-redirector' ) . '</p></div>';
            }
        }
    }    /**
     * Inicializar hooks.
     */
    private function init_hooks() {
        register_activation_hook( CFPR_FILE, array( $this, 'activation' ) );
        register_deactivation_hook( CFPR_FILE, array( $this, 'deactivation' ) );
        
        add_action( 'plugins_loaded', array( $this, 'load_plugin' ) );
        add_action( 'init', array( $this, 'add_custom_capabilities' ) );
    }    /**
     * Adicionar capacidades customizadas.
     */
    public function add_custom_capabilities() {
        // Adicionar a capacidade cartflows_manage_flows_steps para administradores
        $role = get_role( 'administrator' );
        if ( $role ) {
            $role->add_cap( 'cartflows_manage_flows_steps' );
        }
        
        // Adicionar também para editores se existir
        $editor_role = get_role( 'editor' );
        if ( $editor_role ) {
            $editor_role->add_cap( 'cartflows_manage_flows_steps' );
        }
    }

    /**
     * Inicializar componentes.
     */
    private function init_components() {
        // Iniciar o redirecionador
        CFPR_Redirector::get_instance();
        
        // Iniciar a integração
        CFPR_Integration::get_instance();
        
        // Iniciar admin
        if ( is_admin() ) {
            CFPR_Admin::get_instance();
        }
    }    /**
     * Carregar plugin após todos os plugins carregados.
     */
    public function load_plugin() {
        // Sempre iniciar os componentes, mesmo sem CartFlows
        $this->init_components();
    }    /**
     * Verificar plugins necessários.
     */
    public function check_required_plugins() {
        // Verificar apenas se estamos na página do plugin
        if ( isset( $_GET['page'] ) && ( $_GET['page'] === 'cf-payment-redirector' || $_GET['page'] === 'cfpr-settings' ) ) {
            if ( ! class_exists( 'Cartflows_Loader' ) ) {
                echo '<div class="notice notice-warning"><p>' . __( 'O plugin Redirecionador TunaPix funciona melhor com o plugin CartFlows ativo.', 'cf-payment-redirector' ) . '</p></div>';
            }

            if ( ! class_exists( 'WooCommerce' ) ) {
                echo '<div class="notice notice-error"><p>' . __( 'O plugin Redirecionador TunaPix requer o plugin WooCommerce ativo.', 'cf-payment-redirector' ) . '</p></div>';
            }
        }
    }