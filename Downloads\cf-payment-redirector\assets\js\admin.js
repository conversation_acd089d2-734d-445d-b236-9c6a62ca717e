/**
 * Admin JS para CartFlows Payment Redirector - UI Minimalista
 */
(function($) {
    'use strict';

    // Quando o documento estiver pronto
    $(document).ready(function() {
        
        // Inicializar funcionalidades
        initFlowCards();
        initStepSelects();
        initFormValidation();
        
        // Animações suaves para os cards
        $('.cfpr-flow-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });
    });

    /**
     * Inicializar cards dos funis
     */
    function initFlowCards() {
        $('.cfpr-flow-card').on('mouseenter', function() {
            $(this).addClass('cfpr-card-hover');
        }).on('mouseleave', function() {
            $(this).removeClass('cfpr-card-hover');
        });
    }

    /**
     * Inicializar selects de passos
     */
    function initStepSelects() {
        $('.cfpr-step-select').on('focus', function() {
            var select = $(this);
            var flowId = select.data('flow-id');
            
            // Verificar se já carregamos os dados antes
            if (select.hasClass('loaded')) {
                return;
            }
            
            // Marcamos como carregado para não buscar novamente
            select.addClass('loaded');
            
            // Adicionar estado de loading
            select.prop('disabled', true);
            select.find('option:first').text('Carregando passos...');
            
            // Obter passos deste fluxo via AJAX
            $.ajax({
                url: cfpr_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfpr_get_flow_steps',
                    flow_id: flowId,
                    nonce: cfpr_admin.nonce
                },
                success: function(response) {
                    // Se a resposta for bem-sucedida e houver passos
                    if (response.success && response.data.steps) {
                        // Salvar valor atual
                        var currentValue = select.val();
                        
                        // Limpar as opções antigas e adicionar a opção padrão
                        select.empty().append('<option value="">Nenhum redirecionamento</option>');
                        
                        // Adicionar os novos passos
                        $.each(response.data.steps, function(stepId, stepTitle) {
                            select.append('<option value="' + stepId + '">' + stepTitle + '</option>');
                        });
                        
                        // Restaurar valor selecionado
                        if (currentValue) {
                            select.val(currentValue);
                        }
                        
                        // Atualizar status do card
                        updateCardStatus(select.closest('.cfpr-flow-card'));
                        
                    } else {
                        select.empty().append('<option value="">Erro: Nenhum passo encontrado</option>');
                    }
                },
                error: function() {
                    select.empty().append('<option value="">Erro ao carregar passos</option>');
                },
                complete: function() {
                    select.prop('disabled', false);
                }
            });
        });

        // Atualizar status quando mudar seleção
        $('.cfpr-step-select').on('change', function() {
            var card = $(this).closest('.cfpr-flow-card');
            updateCardStatus(card);
        });
    }

    /**
     * Atualizar status do card baseado nas configurações
     */
    function updateCardStatus(card) {
        var selects = card.find('.cfpr-step-select');
        var hasConfig = false;
        
        selects.each(function() {
            if ($(this).val() !== '') {
                hasConfig = true;
                return false; // break
            }
        });
        
        var statusElement = card.find('.cfpr-config-status');
        
        if (hasConfig) {
            statusElement.removeClass('cfpr-status-pending').addClass('cfpr-status-configured');
            statusElement.find('span:first').text('✓');
            statusElement.find('span:last').text('Configurado');
        } else {
            statusElement.removeClass('cfpr-status-configured').addClass('cfpr-status-pending');
            statusElement.find('span:first').text('⚠');
            statusElement.find('span:last').text('Pendente');
        }
    }

    /**
     * Inicializar validação do formulário
     */
    function initFormValidation() {
        $('.cfpr-form').on('submit', function(e) {
            var form = $(this);
            var saveBtn = form.find('.cfpr-save-btn');
            
            // Adicionar estado de loading
            saveBtn.prop('disabled', true);
            saveBtn.text('Salvando...');
            
            // Mostrar feedback visual
            showSaveNotification();
        });
    }

    /**
     * Mostrar notificação de salvamento
     */
    function showSaveNotification() {
        // Criar notificação temporária
        var notification = $('<div class="cfpr-save-notification">Configurações salvas com sucesso!</div>');
        
        notification.css({
            position: 'fixed',
            top: '32px',
            right: '32px',
            background: '#059669',
            color: '#fff',
            padding: '12px 20px',
            borderRadius: '8px',
            fontWeight: '500',
            fontSize: '14px',
            zIndex: '9999',
            opacity: '0',
            transform: 'translateY(-20px)',
            transition: 'all 0.3s ease'
        });
        
        $('body').append(notification);
        
        // Animar entrada
        setTimeout(function() {
            notification.css({
                opacity: '1',
                transform: 'translateY(0)'
            });
        }, 100);
        
        // Remover após 3 segundos
        setTimeout(function() {
            notification.css({
                opacity: '0',
                transform: 'translateY(-20px)'
            });
            
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 3000);
    }

    /**
     * Utilitário para debounce
     */
    function debounce(func, wait) {
        var timeout;
        return function executedFunction() {
            var context = this;
            var args = arguments;
            var later = function() {
                timeout = null;
                func.apply(context, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }



})(jQuery); 